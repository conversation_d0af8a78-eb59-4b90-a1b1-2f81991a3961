import { useQuery } from '@tanstack/react-query';
import { supabase } from '../lib/supabase';
import type { Database } from '../types/database';

// Type definitions for contractor data
type User = Database['public']['Tables']['users']['Row'];
type Contractor = Database['public']['Tables']['contractors']['Row'];

export interface ContractorProfileData {
  user: User | null;
  contractor: Contractor | null;
}

/**
 * Hook to get contractor-specific profile data including contractor company information
 * Only fetches data if the user is a contractor
 */
export function useContractorProfile(userId?: string, isContractor?: boolean) {
  return useQuery({
    queryKey: ['contractor-profile', userId],
    queryFn: async (): Promise<ContractorProfileData> => {
      if (!userId) throw new Error('User ID is required');

      // Get user data with contractor information
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError) {
        console.error('Error fetching user:', userError);
        throw userError;
      }

      // If user has a contractor_id, fetch the contractor company details
      let contractor = null;
      if (user?.contractor_id) {
        const { data: contractorData, error: contractorError } = await supabase
          .from('contractors')
          .select('*')
          .eq('id', user.contractor_id)
          .single();

        if (contractorError && contractorError.code !== 'PGRST116') {
          console.error('Error fetching contractor:', contractorError);
          throw contractorError;
        }

        contractor = contractorData;
      }

      return {
        user,
        contractor,
      };
    },
    enabled: !!userId && !!isContractor,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to get contractor company details by company ID
 */
export function useContractorCompany(companyId?: string) {
  return useQuery({
    queryKey: ['contractor-company', companyId],
    queryFn: async (): Promise<Contractor | null> => {
      if (!companyId) throw new Error('Company ID is required');

      const { data, error } = await supabase
        .from('contractors')
        .select('*')
        .eq('id', companyId)
        .is('deleted_at', null)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // Company not found
        }
        throw error;
      }

      return data;
    },
    enabled: !!companyId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get all contractor companies (for dropdowns, etc.)
 */
export function useContractorCompanies() {
  return useQuery({
    queryKey: ['contractor-companies'],
    queryFn: async (): Promise<Contractor[]> => {
      const { data, error } = await supabase
        .from('contractors')
        .select('*')
        .is('deleted_at', null)
        .eq('is_active', true)
        .order('name', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Hook to get all team members (users) for a specific contractor company
 */
export function useContractorTeamMembers(contractorId?: string) {
  return useQuery({
    queryKey: ['contractor-team-members', contractorId],
    queryFn: async (): Promise<User[]> => {
      if (!contractorId) throw new Error('Contractor ID is required');

      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('contractor_id', contractorId)
        .is('deleted_at', null)
        .order('created_at', { ascending: true });

      if (error) throw error;
      return data || [];
    },
    enabled: !!contractorId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
